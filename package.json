{"name": "nuxt-app", "private": true, "type": "module", "scripts": {"build": "nuxt build", "dev": "nuxt dev", "generate": "nuxt generate", "preview": "nuxt preview", "postinstall": "nuxt prepare"}, "dependencies": {"@nuxt/content": "^3.6.3", "@nuxt/eslint": "^1.7.1", "@nuxt/image": "^1.10.0", "@nuxt/scripts": "^0.11.10", "@nuxt/test-utils": "^3.19.2", "@nuxt/ui": "^3.3.0", "@unhead/vue": "^2.0.12", "eslint": "^9.32.0", "nuxt": "^4.0.1", "typescript": "^5.8.3", "vue": "^3.5.18", "vue-router": "^4.5.1"}}